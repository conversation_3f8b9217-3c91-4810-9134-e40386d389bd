#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多帧跟踪器测试脚本
测试多帧融合与跟踪功能的稳定性和准确性
"""

import numpy as np
import cv2
import time
from typing import List
import matplotlib.pyplot as plt

from yolo_detector import Detection
from multi_frame_tracker import MultiFrameTracker


def create_test_detection(x: int, y: int, w: int, h: int, 
                         confidence: float, class_name: str, class_id: int = 0) -> Detection:
    """创建测试用的检测结果"""
    return Detection(
        box=np.array([x, y, x + w, y + h]),
        confidence=confidence,
        class_id=class_id,
        class_name=class_name
    )


def simulate_detection_sequence() -> List[List[Detection]]:
    """模拟一系列检测结果，包含抖动、丢失等情况"""
    sequences = []
    
    # 模拟场景：一个稳定的物体，一个不稳定的物体
    base_x1, base_y1 = 100, 100  # 稳定物体的基准位置
    base_x2, base_y2 = 300, 200  # 不稳定物体的基准位置
    
    for frame_id in range(50):
        detections = []
        
        # 稳定物体：小幅抖动
        if frame_id < 45:  # 前45帧都能检测到
            noise_x = np.random.randint(-5, 6)
            noise_y = np.random.randint(-5, 6)
            confidence = 0.8 + np.random.random() * 0.15
            
            stable_detection = create_test_detection(
                base_x1 + noise_x, base_y1 + noise_y, 80, 60,
                confidence, "bottle", 0
            )
            detections.append(stable_detection)
        
        # 不稳定物体：大幅抖动，间歇性丢失
        if frame_id % 3 != 0:  # 每3帧丢失一次
            noise_x = np.random.randint(-15, 16)
            noise_y = np.random.randint(-15, 16)
            confidence = 0.3 + np.random.random() * 0.4
            
            unstable_detection = create_test_detection(
                base_x2 + noise_x, base_y2 + noise_y, 60, 80,
                confidence, "can", 1
            )
            detections.append(unstable_detection)
        
        # 新出现的物体（从第20帧开始）
        if frame_id >= 20 and frame_id < 40:
            confidence = 0.6 + np.random.random() * 0.2
            new_detection = create_test_detection(
                500, 150, 70, 70, confidence, "orange", 2
            )
            detections.append(new_detection)
        
        sequences.append(detections)
    
    return sequences


def test_tracking_stability():
    """测试跟踪稳定性"""
    print("🧪 测试多帧跟踪器稳定性...")
    
    # 创建跟踪器
    config = {
        'iou_threshold': 0.5,
        'max_miss_frames': 5,
        'min_stable_frames': 3,
        'confidence_smooth_factor': 0.7,
        'bbox_smooth_factor': 0.8,
        'enable_confidence_boost': True,
    }
    tracker = MultiFrameTracker(config)
    
    # 生成测试序列
    detection_sequences = simulate_detection_sequence()
    
    # 统计数据
    original_counts = []
    stable_counts = []
    stability_rates = []
    
    print("\n📊 逐帧处理结果:")
    print("帧号 | 原始检测 | 稳定检测 | 活跃跟踪 | 稳定跟踪 | 稳定率")
    print("-" * 65)
    
    for frame_id, detections in enumerate(detection_sequences):
        # 更新跟踪器
        stable_detections = tracker.update(detections)
        
        # 获取统计信息
        tracking_info = tracker.get_tracking_info()
        
        # 记录数据
        original_counts.append(len(detections))
        stable_counts.append(len(stable_detections))
        stability_rates.append(tracking_info['stability_rate'])
        
        # 每5帧打印一次详细信息
        if frame_id % 5 == 0 or frame_id < 10:
            print(f"{frame_id:3d}  | {len(detections):8d} | {len(stable_detections):8d} | "
                  f"{tracking_info['active_tracks']:8d} | {tracking_info['stable_tracks']:8d} | "
                  f"{tracking_info['stability_rate']:6.1f}%")
    
    # 最终统计
    print("\n📈 最终统计结果:")
    final_info = tracker.get_detailed_tracking_info()
    
    print(f"总处理帧数: {len(detection_sequences)}")
    print(f"总检测数量: {final_info['basic_info']['total_detections']}")
    print(f"稳定检测数量: {final_info['basic_info']['stable_detections']}")
    print(f"整体稳定率: {final_info['basic_info']['stability_rate']:.1f}%")
    
    print(f"\n各类别统计:")
    for class_name, stats in final_info['class_statistics'].items():
        print(f"  {class_name}:")
        print(f"    总跟踪数: {stats['total']}")
        print(f"    稳定跟踪数: {stats['stable']}")
        print(f"    平均置信度: {stats['avg_confidence']:.3f}")
        print(f"    平均连续检测: {stats['avg_detections']:.1f}")
    
    return original_counts, stable_counts, stability_rates


def test_confidence_boost():
    """测试置信度提升功能"""
    print("\n🚀 测试置信度提升功能...")
    
    config = {
        'iou_threshold': 0.5,
        'max_miss_frames': 3,
        'min_stable_frames': 3,
        'enable_confidence_boost': True,
    }
    tracker = MultiFrameTracker(config)
    
    # 创建一个低置信度但稳定的检测序列
    low_conf_detections = []
    for i in range(10):
        detection = create_test_detection(100, 100, 80, 60, 0.4, "bottle", 0)
        low_conf_detections.append([detection])
    
    print("原始置信度序列:")
    for i, detections in enumerate(low_conf_detections):
        stable_detections = tracker.update(detections)
        
        if stable_detections:
            original_conf = detections[0].confidence
            stable_conf = stable_detections[0].confidence
            boost = stable_conf / original_conf if original_conf > 0 else 1.0
            
            print(f"帧 {i:2d}: {original_conf:.3f} -> {stable_conf:.3f} (提升: {boost:.2f}x)")


def visualize_tracking_results():
    """可视化跟踪结果"""
    print("\n📊 生成跟踪结果可视化...")
    
    original_counts, stable_counts, stability_rates = test_tracking_stability()
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 检测数量对比
    frames = range(len(original_counts))
    ax1.plot(frames, original_counts, 'b-', label='原始检测', alpha=0.7)
    ax1.plot(frames, stable_counts, 'r-', label='稳定检测', linewidth=2)
    ax1.set_xlabel('帧号')
    ax1.set_ylabel('检测数量')
    ax1.set_title('多帧跟踪效果对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 稳定率变化
    ax2.plot(frames, stability_rates, 'g-', linewidth=2)
    ax2.set_xlabel('帧号')
    ax2.set_ylabel('稳定率 (%)')
    ax2.set_title('跟踪稳定率变化')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)
    
    plt.tight_layout()
    plt.savefig('output/tracking_results.png', dpi=300, bbox_inches='tight')
    print("📊 可视化结果已保存到 output/tracking_results.png")
    
    return fig


def test_mask_fusion():
    """测试掩膜融合功能"""
    print("\n🎭 测试掩膜融合功能...")
    
    # 创建测试掩膜
    mask1 = np.zeros((100, 100), dtype=np.uint8)
    mask1[30:70, 30:70] = 1
    
    mask2 = np.zeros((100, 100), dtype=np.uint8)
    mask2[25:75, 25:75] = 1
    
    mask3 = np.zeros((100, 100), dtype=np.uint8)
    mask3[35:65, 35:65] = 1
    
    # 创建带掩膜的检测结果
    detection1 = create_test_detection(100, 100, 80, 60, 0.8, "bottle", 0)
    detection1.mask = mask1
    
    detection2 = create_test_detection(102, 98, 80, 60, 0.7, "bottle", 0)
    detection2.mask = mask2
    
    detection3 = create_test_detection(98, 102, 80, 60, 0.9, "bottle", 0)
    detection3.mask = mask3
    
    # 创建跟踪器并处理
    config = {'enable_mask_fusion': True}
    tracker = MultiFrameTracker(config)
    
    # 逐帧处理
    for i, detection in enumerate([detection1, detection2, detection3]):
        stable_detections = tracker.update([detection])
        print(f"帧 {i+1}: 输入掩膜面积 {np.sum(detection.mask)}")
        
        if stable_detections and stable_detections[0].mask is not None:
            fused_area = np.sum(stable_detections[0].mask)
            print(f"       融合掩膜面积 {fused_area}")


def main():
    """主测试函数"""
    print("🎯 多帧跟踪器综合测试")
    print("=" * 50)
    
    try:
        # 基本稳定性测试
        test_tracking_stability()
        
        # 置信度提升测试
        test_confidence_boost()
        
        # 掩膜融合测试
        test_mask_fusion()
        
        # 生成可视化结果
        try:
            visualize_tracking_results()
        except ImportError:
            print("⚠️ matplotlib未安装，跳过可视化测试")
        
        print("\n✅ 所有测试完成！")
        print("💡 多帧跟踪器功能正常，可以有效提升检测稳定性")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
