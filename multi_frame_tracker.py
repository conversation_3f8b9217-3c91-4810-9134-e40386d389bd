#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多帧融合与跟踪模块
用于提升YOLO分割模型的识别稳定性
通过多帧融合、目标跟踪和置信度平滑来减少检测结果的抖动
"""

import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import time
from dataclasses import dataclass, field
from collections import deque
import uuid

from yolo_detector import Detection


@dataclass
class TrackedObject:
    """跟踪的物体信息"""
    track_id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    class_name: str = ""
    class_id: int = -1
    
    # 历史信息
    bbox_history: deque = field(default_factory=lambda: deque(maxlen=10))
    confidence_history: deque = field(default_factory=lambda: deque(maxlen=10))
    mask_history: deque = field(default_factory=lambda: deque(maxlen=5))
    
    # 当前状态
    current_bbox: Optional[Tuple[int, int, int, int]] = None
    current_confidence: float = 0.0
    current_mask: Optional[np.ndarray] = None
    
    # 跟踪状态
    last_seen_frame: int = 0
    consecutive_detections: int = 0
    consecutive_misses: int = 0
    is_stable: bool = False
    
    # 融合后的稳定属性
    stable_bbox: Optional[Tuple[int, int, int, int]] = None
    stable_confidence: float = 0.0
    stable_mask: Optional[np.ndarray] = None
    
    def update(self, detection: Detection, frame_id: int):
        """更新跟踪物体信息"""
        self.current_bbox = detection.bbox
        self.current_confidence = detection.confidence
        self.current_mask = detection.mask
        self.last_seen_frame = frame_id
        self.consecutive_detections += 1
        self.consecutive_misses = 0
        
        # 更新历史记录
        self.bbox_history.append(detection.bbox)
        self.confidence_history.append(detection.confidence)
        if detection.mask is not None:
            self.mask_history.append(detection.mask.copy())
        
        # 更新稳定状态
        self._update_stable_properties()
    
    def miss_detection(self, frame_id: int):
        """标记检测丢失"""
        self.consecutive_misses += 1
        self.consecutive_detections = 0
        
        # 如果连续丢失帧数不多，保持稳定属性
        if self.consecutive_misses <= 3 and self.is_stable:
            # 保持上一帧的稳定属性
            pass
        else:
            self.is_stable = False
    
    def _update_stable_properties(self):
        """更新稳定的融合属性"""
        if len(self.bbox_history) < 3:
            self.is_stable = False
            return
        
        # 计算稳定的边界框（使用中位数滤波）
        if len(self.bbox_history) >= 3:
            bboxes = np.array(list(self.bbox_history))
            self.stable_bbox = tuple(np.median(bboxes, axis=0).astype(int))
        
        # 计算稳定的置信度（使用加权平均）
        if len(self.confidence_history) >= 3:
            confidences = np.array(list(self.confidence_history))
            weights = np.linspace(0.5, 1.0, len(confidences))  # 近期帧权重更高
            self.stable_confidence = np.average(confidences, weights=weights)
        
        # 计算稳定的掩膜（使用多数投票）
        if len(self.mask_history) >= 3:
            self.stable_mask = self._fuse_masks(list(self.mask_history))
        
        # 判断是否稳定
        self.is_stable = (
            self.consecutive_detections >= 3 and
            self.consecutive_misses == 0 and
            len(self.bbox_history) >= 3
        )
    
    def _fuse_masks(self, masks: List[np.ndarray]) -> Optional[np.ndarray]:
        """融合多个掩膜"""
        if not masks:
            return None
        
        try:
            # 确保所有掩膜尺寸一致
            reference_shape = masks[0].shape
            valid_masks = []
            
            for mask in masks:
                if mask.shape == reference_shape:
                    valid_masks.append(mask.astype(np.float32))
            
            if not valid_masks:
                return None
            
            # 使用加权平均融合掩膜
            weights = np.linspace(0.5, 1.0, len(valid_masks))
            fused_mask = np.zeros_like(valid_masks[0])
            
            for i, mask in enumerate(valid_masks):
                fused_mask += mask * weights[i]
            
            # 归一化并二值化
            fused_mask = fused_mask / np.sum(weights)
            return (fused_mask > 0.5).astype(np.uint8)
            
        except Exception as e:
            print(f"⚠️ 掩膜融合失败: {e}")
            return masks[-1] if masks else None
    
    def get_stable_detection(self) -> Optional[Detection]:
        """获取稳定的检测结果"""
        if not self.is_stable or self.stable_bbox is None:
            return None
        
        return Detection(
            box=np.array(self.stable_bbox),
            confidence=self.stable_confidence,
            class_id=self.class_id,
            class_name=self.class_name,
            mask=self.stable_mask
        )
    
    def should_remove(self, max_miss_frames: int = 5) -> bool:
        """判断是否应该移除此跟踪对象"""
        return self.consecutive_misses > max_miss_frames


class MultiFrameTracker:
    """多帧融合与跟踪器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化多帧跟踪器
        
        Args:
            config: 配置参数
        """
        # 默认配置
        default_config = {
            'iou_threshold': 0.5,           # IoU阈值用于匹配检测结果
            'max_miss_frames': 5,           # 最大丢失帧数
            'min_stable_frames': 3,         # 最小稳定帧数
            'confidence_smooth_factor': 0.7, # 置信度平滑因子
            'bbox_smooth_factor': 0.8,      # 边界框平滑因子
            'enable_kalman_filter': False,  # 是否启用卡尔曼滤波
        }
        
        if config:
            default_config.update(config)
        self.config = default_config
        
        # 跟踪状态
        self.tracked_objects: Dict[str, TrackedObject] = {}
        self.frame_id = 0
        
        # 统计信息
        self.total_detections = 0
        self.stable_detections = 0
        
        print("🎯 多帧融合与跟踪器初始化完成")
        print(f"   IoU阈值: {self.config['iou_threshold']}")
        print(f"   最大丢失帧数: {self.config['max_miss_frames']}")
        print(f"   最小稳定帧数: {self.config['min_stable_frames']}")
    
    def update(self, detections: List[Detection]) -> List[Detection]:
        """
        更新跟踪器并返回稳定的检测结果
        
        Args:
            detections: 当前帧的检测结果
            
        Returns:
            稳定的检测结果列表
        """
        self.frame_id += 1
        self.total_detections += len(detections)
        
        # 1. 匹配当前检测结果与已跟踪对象
        matched_pairs, unmatched_detections, unmatched_tracks = self._match_detections(detections)
        
        # 2. 更新匹配的跟踪对象
        for track_id, detection in matched_pairs:
            self.tracked_objects[track_id].update(detection, self.frame_id)
        
        # 3. 为未匹配的检测结果创建新的跟踪对象
        for detection in unmatched_detections:
            self._create_new_track(detection)
        
        # 4. 标记未匹配的跟踪对象为丢失
        for track_id in unmatched_tracks:
            self.tracked_objects[track_id].miss_detection(self.frame_id)
        
        # 5. 移除长时间丢失的跟踪对象
        self._remove_lost_tracks()
        
        # 6. 获取稳定的检测结果
        stable_detections = self._get_stable_detections()
        self.stable_detections += len(stable_detections)
        
        # 7. 打印统计信息
        if self.frame_id % 10 == 0:
            self._print_statistics()
        
        return stable_detections
    
    def _match_detections(self, detections: List[Detection]) -> Tuple[List[Tuple[str, Detection]], List[Detection], List[str]]:
        """匹配检测结果与跟踪对象"""
        if not self.tracked_objects or not detections:
            return [], detections, list(self.tracked_objects.keys())
        
        # 计算IoU矩阵
        track_ids = list(self.tracked_objects.keys())
        iou_matrix = np.zeros((len(track_ids), len(detections)))
        
        for i, track_id in enumerate(track_ids):
            track_obj = self.tracked_objects[track_id]
            if track_obj.current_bbox is not None:
                for j, detection in enumerate(detections):
                    iou = self._calculate_iou(track_obj.current_bbox, detection.bbox)
                    # 同类别才考虑匹配
                    if track_obj.class_name == detection.class_name:
                        iou_matrix[i, j] = iou
        
        # 使用贪心算法进行匹配
        matched_pairs = []
        unmatched_detections = list(detections)
        unmatched_tracks = list(track_ids)
        
        # 按IoU从高到低排序进行匹配
        while True:
            max_iou = np.max(iou_matrix)
            if max_iou < self.config['iou_threshold']:
                break
            
            # 找到最大IoU的位置
            max_pos = np.unravel_index(np.argmax(iou_matrix), iou_matrix.shape)
            track_idx, det_idx = max_pos
            
            # 添加匹配对
            track_id = track_ids[track_idx]
            detection = detections[det_idx]
            matched_pairs.append((track_id, detection))
            
            # 从未匹配列表中移除
            if detection in unmatched_detections:
                unmatched_detections.remove(detection)
            if track_id in unmatched_tracks:
                unmatched_tracks.remove(track_id)
            
            # 将对应行列设为0
            iou_matrix[track_idx, :] = 0
            iou_matrix[:, det_idx] = 0
        
        return matched_pairs, unmatched_detections, unmatched_tracks
    
    def _calculate_iou(self, bbox1: Tuple[int, int, int, int], bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _create_new_track(self, detection: Detection):
        """创建新的跟踪对象"""
        track_obj = TrackedObject(
            class_name=detection.class_name,
            class_id=detection.class_id
        )
        track_obj.update(detection, self.frame_id)
        self.tracked_objects[track_obj.track_id] = track_obj
    
    def _remove_lost_tracks(self):
        """移除长时间丢失的跟踪对象"""
        to_remove = []
        for track_id, track_obj in self.tracked_objects.items():
            if track_obj.should_remove(self.config['max_miss_frames']):
                to_remove.append(track_id)
        
        for track_id in to_remove:
            del self.tracked_objects[track_id]
    
    def _get_stable_detections(self) -> List[Detection]:
        """获取稳定的检测结果"""
        stable_detections = []
        
        for track_obj in self.tracked_objects.values():
            stable_detection = track_obj.get_stable_detection()
            if stable_detection is not None:
                stable_detections.append(stable_detection)
        
        return stable_detections
    
    def _print_statistics(self):
        """打印统计信息"""
        active_tracks = len(self.tracked_objects)
        stable_tracks = sum(1 for obj in self.tracked_objects.values() if obj.is_stable)
        stability_rate = (self.stable_detections / max(self.total_detections, 1)) * 100
        
        print(f"📊 跟踪统计 (帧 {self.frame_id}):")
        print(f"   活跃跟踪: {active_tracks} 个")
        print(f"   稳定跟踪: {stable_tracks} 个")
        print(f"   稳定率: {stability_rate:.1f}%")
    
    def get_tracking_info(self) -> Dict[str, Any]:
        """获取跟踪信息"""
        return {
            'frame_id': self.frame_id,
            'active_tracks': len(self.tracked_objects),
            'stable_tracks': sum(1 for obj in self.tracked_objects.values() if obj.is_stable),
            'total_detections': self.total_detections,
            'stable_detections': self.stable_detections,
            'stability_rate': (self.stable_detections / max(self.total_detections, 1)) * 100
        }

    def get_detailed_tracking_info(self) -> Dict[str, Any]:
        """获取详细的跟踪信息"""
        class_stats = {}
        for obj in self.tracked_objects.values():
            class_name = obj.class_name
            if class_name not in class_stats:
                class_stats[class_name] = {
                    'total': 0,
                    'stable': 0,
                    'avg_confidence': 0.0,
                    'avg_detections': 0
                }

            class_stats[class_name]['total'] += 1
            if obj.is_stable:
                class_stats[class_name]['stable'] += 1
            class_stats[class_name]['avg_confidence'] += obj.stable_confidence
            class_stats[class_name]['avg_detections'] += obj.consecutive_detections

        # 计算平均值
        for stats in class_stats.values():
            if stats['total'] > 0:
                stats['avg_confidence'] /= stats['total']
                stats['avg_detections'] /= stats['total']

        return {
            'basic_info': self.get_tracking_info(),
            'class_statistics': class_stats,
            'track_details': [
                {
                    'track_id': obj.track_id,
                    'class_name': obj.class_name,
                    'is_stable': obj.is_stable,
                    'consecutive_detections': obj.consecutive_detections,
                    'consecutive_misses': obj.consecutive_misses,
                    'stable_confidence': obj.stable_confidence,
                    'bbox_history_length': len(obj.bbox_history)
                }
                for obj in self.tracked_objects.values()
            ]
        }

    def apply_confidence_boost(self, detections: List[Detection]) -> List[Detection]:
        """
        对稳定跟踪的物体应用置信度提升

        Args:
            detections: 检测结果列表

        Returns:
            置信度提升后的检测结果列表
        """
        if not self.config.get('enable_confidence_boost', True):
            return detections

        boosted_detections = []

        for detection in detections:
            # 查找对应的跟踪对象
            matching_track = None
            for obj in self.tracked_objects.values():
                if (obj.class_name == detection.class_name and
                    obj.current_bbox is not None and
                    self._calculate_iou(obj.current_bbox, detection.bbox) > 0.5):
                    matching_track = obj
                    break

            # 如果找到稳定的跟踪对象，提升置信度
            if matching_track and matching_track.is_stable:
                boost_factor = min(1.2, 1.0 + 0.1 * (matching_track.consecutive_detections - 3))
                new_confidence = min(1.0, detection.confidence * boost_factor)

                boosted_detection = Detection(
                    box=detection.box,
                    confidence=new_confidence,
                    class_id=detection.class_id,
                    class_name=detection.class_name,
                    mask=detection.mask
                )
                boosted_detections.append(boosted_detection)
            else:
                boosted_detections.append(detection)

        return boosted_detections

    def get_prediction_for_missing_objects(self) -> List[Detection]:
        """
        为短期丢失的稳定对象生成预测检测结果

        Returns:
            预测的检测结果列表
        """
        predicted_detections = []

        for obj in self.tracked_objects.values():
            # 只为稳定且短期丢失的对象生成预测
            if (obj.is_stable and
                obj.consecutive_misses > 0 and
                obj.consecutive_misses <= 2 and
                obj.stable_bbox is not None):

                # 使用稳定的属性创建预测检测结果
                predicted_detection = Detection(
                    box=np.array(obj.stable_bbox),
                    confidence=obj.stable_confidence * 0.8,  # 降低预测的置信度
                    class_id=obj.class_id,
                    class_name=obj.class_name,
                    mask=obj.stable_mask
                )
                predicted_detections.append(predicted_detection)

        return predicted_detections

    def reset(self):
        """重置跟踪器"""
        self.tracked_objects.clear()
        self.frame_id = 0
        self.total_detections = 0
        self.stable_detections = 0
        print("🔄 多帧跟踪器已重置")
