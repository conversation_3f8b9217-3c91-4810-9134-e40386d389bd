#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跟踪可视化模块
用于可视化多帧跟踪的结果，包括轨迹、稳定性指标等
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import colorsys
from collections import defaultdict

from yolo_detector import Detection
from multi_frame_tracker import MultiFrameTracker, TrackedObject


class TrackingVisualizer:
    """跟踪结果可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        self.track_colors = {}  # 跟踪ID到颜色的映射
        self.trajectory_history = defaultdict(list)  # 轨迹历史
        self.max_trajectory_length = 30  # 最大轨迹长度
        
        # 颜色生成器
        self.color_index = 0
        
    def generate_track_color(self, track_id: str) -> Tuple[int, int, int]:
        """为跟踪ID生成唯一颜色"""
        if track_id not in self.track_colors:
            # 使用HSV色彩空间生成不同的颜色
            hue = (self.color_index * 137.5) % 360  # 黄金角度分割
            saturation = 0.8
            value = 0.9
            
            # 转换为RGB
            rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)
            bgr = (int(rgb[2]*255), int(rgb[1]*255), int(rgb[0]*255))
            
            self.track_colors[track_id] = bgr
            self.color_index += 1
        
        return self.track_colors[track_id]
    
    def draw_tracking_results(self, image: np.ndarray, 
                            tracker: MultiFrameTracker,
                            show_trajectories: bool = True,
                            show_track_info: bool = True,
                            show_stability_status: bool = True) -> np.ndarray:
        """
        在图像上绘制跟踪结果
        
        Args:
            image: 输入图像
            tracker: 多帧跟踪器
            show_trajectories: 是否显示轨迹
            show_track_info: 是否显示跟踪信息
            show_stability_status: 是否显示稳定性状态
            
        Returns:
            绘制了跟踪结果的图像
        """
        result_image = image.copy()
        
        # 绘制每个跟踪对象
        for track_obj in tracker.tracked_objects.values():
            if track_obj.current_bbox is None:
                continue
            
            track_color = self.generate_track_color(track_obj.track_id)
            
            # 更新轨迹历史
            center_x = (track_obj.current_bbox[0] + track_obj.current_bbox[2]) // 2
            center_y = (track_obj.current_bbox[1] + track_obj.current_bbox[3]) // 2
            self.trajectory_history[track_obj.track_id].append((center_x, center_y))
            
            # 限制轨迹长度
            if len(self.trajectory_history[track_obj.track_id]) > self.max_trajectory_length:
                self.trajectory_history[track_obj.track_id].pop(0)
            
            # 绘制边界框
            self._draw_tracked_bbox(result_image, track_obj, track_color, show_stability_status)
            
            # 绘制轨迹
            if show_trajectories:
                self._draw_trajectory(result_image, track_obj.track_id, track_color)
            
            # 绘制跟踪信息
            if show_track_info:
                self._draw_track_info(result_image, track_obj, track_color)
        
        # 绘制全局统计信息
        self._draw_global_stats(result_image, tracker)
        
        return result_image
    
    def _draw_tracked_bbox(self, image: np.ndarray, track_obj: TrackedObject, 
                          color: Tuple[int, int, int], show_stability: bool):
        """绘制跟踪对象的边界框"""
        x1, y1, x2, y2 = track_obj.current_bbox
        
        # 根据稳定性选择线条样式
        if track_obj.is_stable:
            thickness = 3
            line_type = cv2.LINE_8
        else:
            thickness = 2
            line_type = cv2.LINE_4
        
        # 绘制边界框
        cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness, line_type)
        
        # 绘制稳定性指示器
        if show_stability:
            if track_obj.is_stable:
                # 稳定：绿色圆点
                cv2.circle(image, (x1 + 10, y1 + 10), 5, (0, 255, 0), -1)
            else:
                # 不稳定：红色三角形
                pts = np.array([[x1 + 5, y1 + 15], [x1 + 15, y1 + 15], [x1 + 10, y1 + 5]], np.int32)
                cv2.fillPoly(image, [pts], (0, 0, 255))
    
    def _draw_trajectory(self, image: np.ndarray, track_id: str, color: Tuple[int, int, int]):
        """绘制轨迹"""
        trajectory = self.trajectory_history[track_id]
        
        if len(trajectory) < 2:
            return
        
        # 绘制轨迹线
        for i in range(1, len(trajectory)):
            # 计算透明度（越新的点越不透明）
            alpha = i / len(trajectory)
            thickness = max(1, int(3 * alpha))
            
            # 调整颜色亮度
            adjusted_color = tuple(int(c * (0.3 + 0.7 * alpha)) for c in color)
            
            cv2.line(image, trajectory[i-1], trajectory[i], adjusted_color, thickness)
        
        # 绘制轨迹点
        for i, point in enumerate(trajectory):
            alpha = (i + 1) / len(trajectory)
            radius = max(1, int(3 * alpha))
            cv2.circle(image, point, radius, color, -1)
    
    def _draw_track_info(self, image: np.ndarray, track_obj: TrackedObject, 
                        color: Tuple[int, int, int]):
        """绘制跟踪信息"""
        x1, y1, x2, y2 = track_obj.current_bbox
        
        # 准备信息文本
        info_lines = [
            f"ID: {track_obj.track_id}",
            f"{track_obj.class_name}",
            f"Conf: {track_obj.current_confidence:.2f}",
            f"Det: {track_obj.consecutive_detections}",
            f"Miss: {track_obj.consecutive_misses}"
        ]
        
        # 计算文本区域
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.4
        thickness = 1
        
        text_height = 15
        max_width = 0
        
        for line in info_lines:
            (text_width, _), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_width = max(max_width, text_width)
        
        # 绘制背景
        bg_x1 = x1
        bg_y1 = y1 - len(info_lines) * text_height - 5
        bg_x2 = x1 + max_width + 10
        bg_y2 = y1
        
        # 确保背景在图像范围内
        bg_y1 = max(0, bg_y1)
        
        cv2.rectangle(image, (bg_x1, bg_y1), (bg_x2, bg_y2), color, -1)
        cv2.rectangle(image, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), 1)
        
        # 绘制文本
        for i, line in enumerate(info_lines):
            text_y = bg_y1 + (i + 1) * text_height
            cv2.putText(image, line, (bg_x1 + 5, text_y), font, font_scale, 
                       (255, 255, 255), thickness)
    
    def _draw_global_stats(self, image: np.ndarray, tracker: MultiFrameTracker):
        """绘制全局统计信息"""
        tracking_info = tracker.get_tracking_info()
        
        # 准备统计信息
        stats_lines = [
            f"Frame: {tracking_info['frame_id']}",
            f"Active Tracks: {tracking_info['active_tracks']}",
            f"Stable Tracks: {tracking_info['stable_tracks']}",
            f"Stability Rate: {tracking_info['stability_rate']:.1f}%"
        ]
        
        # 绘制统计信息背景
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        thickness = 1
        
        text_height = 20
        max_width = 0
        
        for line in stats_lines:
            (text_width, _), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_width = max(max_width, text_width)
        
        # 背景位置（右上角）
        bg_x1 = image.shape[1] - max_width - 20
        bg_y1 = 10
        bg_x2 = image.shape[1] - 10
        bg_y2 = bg_y1 + len(stats_lines) * text_height + 10
        
        # 绘制半透明背景
        overlay = image.copy()
        cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, image, 0.3, 0, image)
        
        cv2.rectangle(image, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), 1)
        
        # 绘制统计文本
        for i, line in enumerate(stats_lines):
            text_y = bg_y1 + (i + 1) * text_height
            cv2.putText(image, line, (bg_x1 + 10, text_y), font, font_scale, 
                       (255, 255, 255), thickness)
    
    def create_tracking_summary_image(self, tracker: MultiFrameTracker, 
                                    image_size: Tuple[int, int] = (800, 600)) -> np.ndarray:
        """
        创建跟踪摘要图像
        
        Args:
            tracker: 多帧跟踪器
            image_size: 图像尺寸 (width, height)
            
        Returns:
            跟踪摘要图像
        """
        width, height = image_size
        summary_image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 获取详细跟踪信息
        detailed_info = tracker.get_detailed_tracking_info()
        
        # 绘制标题
        title = "Multi-Frame Tracking Summary"
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        thickness = 2
        
        (text_width, text_height), _ = cv2.getTextSize(title, font, font_scale, thickness)
        title_x = (width - text_width) // 2
        title_y = 40
        
        cv2.putText(summary_image, title, (title_x, title_y), font, font_scale, 
                   (255, 255, 255), thickness)
        
        # 绘制基本统计信息
        basic_info = detailed_info['basic_info']
        y_offset = 100
        
        basic_stats = [
            f"Total Frames: {basic_info['frame_id']}",
            f"Total Detections: {basic_info['total_detections']}",
            f"Stable Detections: {basic_info['stable_detections']}",
            f"Overall Stability Rate: {basic_info['stability_rate']:.1f}%"
        ]
        
        for line in basic_stats:
            cv2.putText(summary_image, line, (50, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (255, 255, 255), 1)
            y_offset += 30
        
        # 绘制类别统计
        y_offset += 20
        cv2.putText(summary_image, "Class Statistics:", (50, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        y_offset += 40
        
        for class_name, stats in detailed_info['class_statistics'].items():
            class_line = f"{class_name}: {stats['stable']}/{stats['total']} stable " \
                        f"(avg conf: {stats['avg_confidence']:.2f})"
            cv2.putText(summary_image, class_line, (70, y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
            y_offset += 25
        
        return summary_image
    
    def reset(self):
        """重置可视化器"""
        self.track_colors.clear()
        self.trajectory_history.clear()
        self.color_index = 0
